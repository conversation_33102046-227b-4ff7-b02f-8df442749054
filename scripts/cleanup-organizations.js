const mongoose = require('mongoose');

// MongoDB connection string
const MONGODB_URI = "mongodb+srv://mrleritaite:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";

async function cleanupOrganizations() {
  try {
    console.log('🔄 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    const db = mongoose.connection.db;
    const organizationsCollection = db.collection('organizations');

    // Find all organizations
    const allOrgs = await organizationsCollection.find({}).toArray();
    console.log(`Found ${allOrgs.length} organizations:`);
    
    allOrgs.forEach(org => {
      console.log(`- ID: ${org._id}, org_id: ${org.org_id}, user_id: ${org.user_id}, provider: ${org.provider}`);
    });

    // Delete organizations with incorrect user_id (GitLab user ID instead of NextAuth user ID)
    const deleteResult = await organizationsCollection.deleteMany({
      user_id: '9612529' // This is the GitLab user ID, not the NextAuth user ID
    });

    console.log(`Deleted ${deleteResult.deletedCount} organizations with incorrect user_id`);

    // Drop the problematic unique index
    try {
      await organizationsCollection.dropIndex('installation_id_1');
      console.log('✅ Dropped installation_id_1 index');
    } catch (error) {
      console.log('Index installation_id_1 does not exist or already dropped');
    }

    // List remaining organizations
    const remainingOrgs = await organizationsCollection.find({}).toArray();
    console.log(`Remaining ${remainingOrgs.length} organizations:`);
    
    remainingOrgs.forEach(org => {
      console.log(`- ID: ${org._id}, org_id: ${org.org_id}, user_id: ${org.user_id}, provider: ${org.provider}`);
    });

    console.log('✅ Cleanup completed');
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
  }
}

cleanupOrganizations();
